"""
API Service Factory for managing different game API services
"""
from typing import Dict, Optional, Type, List
from flask import current_app
from app.services.base_api import BaseAPIService
from app.services.riot_api import RiotAPIService
from app.services.steam_api import SteamAPIService
from app.services.bgmi_api import BGMIAPIService


class APIServiceFactory:
    """Factory class for creating and managing API service instances"""
    
    _services: Dict[str, BaseAPIService] = {}
    _service_classes: Dict[str, Type[BaseAPIService]] = {
        'valorant': RiotAPIService,
        'cs2': SteamAPIService,
        'bgmi': BGMIAPIService
    }
    
    @classmethod
    def get_service(cls, game_name: str) -> Optional[BaseAPIService]:
        """Get API service instance for a specific game"""
        game_name = game_name.lower()
        
        if game_name not in cls._service_classes:
            current_app.logger.warning(f"No API service available for game: {game_name}")
            return None
        
        # Return cached instance if available
        if game_name in cls._services:
            return cls._services[game_name]
        
        # Create new instance
        try:
            service_class = cls._service_classes[game_name]
            api_key = cls._get_api_key(game_name)
            
            if not api_key and game_name != 'bgmi':  # BGMI doesn't require API key
                current_app.logger.error(f"No API key configured for {game_name}")
                return None
            
            service = service_class(api_key)
            cls._services[game_name] = service
            
            current_app.logger.info(f"Created API service for {game_name}")
            return service
            
        except Exception as e:
            current_app.logger.error(f"Failed to create API service for {game_name}: {e}")
            return None
    
    @classmethod
    def _get_api_key(cls, game_name: str) -> Optional[str]:
        """Get API key for a specific game from configuration"""
        key_mapping = {
            'valorant': 'RIOT_API_KEY',
            'cs2': 'STEAM_API_KEY',
            'bgmi': None  # BGMI doesn't have an official API
        }
        
        config_key = key_mapping.get(game_name)
        if not config_key:
            return None
        
        return current_app.config.get(config_key)
    
    @classmethod
    def get_available_games(cls) -> List[str]:
        """Get list of games with available API services"""
        available = []
        
        for game_name in cls._service_classes.keys():
            # Check if API key is available (except for BGMI)
            if game_name == 'bgmi' or cls._get_api_key(game_name):
                available.append(game_name)
        
        return available
    
    @classmethod
    def test_service(cls, game_name: str) -> Dict[str, any]:
        """Test API service connectivity and configuration"""
        service = cls.get_service(game_name)
        
        if not service:
            return {
                'success': False,
                'error': f'Service not available for {game_name}',
                'details': 'API service could not be created'
            }
        
        try:
            # Test basic connectivity
            if hasattr(service, 'test_connection'):
                result = service.test_connection()
            else:
                # Basic test - try to validate a dummy identifier
                result = service.validate_player_identifier('test123')
                result = {'success': True, 'message': 'Service initialized successfully'}
            
            return {
                'success': True,
                'service_class': service.__class__.__name__,
                'game': game_name,
                'details': result
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'service_class': service.__class__.__name__,
                'game': game_name
            }
    
    @classmethod
    def clear_cache(cls, game_name: str = None) -> None:
        """Clear cached service instances"""
        if game_name:
            cls._services.pop(game_name.lower(), None)
        else:
            cls._services.clear()
        
        current_app.logger.info(f"Cleared API service cache for {game_name or 'all games'}")
    
    @classmethod
    def get_service_status(cls) -> Dict[str, Dict]:
        """Get status of all API services"""
        status = {}
        
        for game_name in cls._service_classes.keys():
            try:
                test_result = cls.test_service(game_name)
                status[game_name] = {
                    'available': test_result['success'],
                    'service_class': test_result.get('service_class'),
                    'error': test_result.get('error'),
                    'has_api_key': bool(cls._get_api_key(game_name)) or game_name == 'bgmi'
                }
            except Exception as e:
                status[game_name] = {
                    'available': False,
                    'error': str(e),
                    'has_api_key': False
                }
        
        return status


# Convenience functions for common operations
def get_valorant_service() -> Optional[RiotAPIService]:
    """Get Valorant API service"""
    return APIServiceFactory.get_service('valorant')


def get_cs2_service() -> Optional[SteamAPIService]:
    """Get CS2 API service"""
    return APIServiceFactory.get_service('cs2')


def get_bgmi_service() -> Optional[BGMIAPIService]:
    """Get BGMI API service"""
    return APIServiceFactory.get_service('bgmi')


def get_service_for_game(game_name: str) -> Optional[BaseAPIService]:
    """Get API service for any supported game"""
    return APIServiceFactory.get_service(game_name)


# Service health check function
def check_all_services() -> Dict[str, bool]:
    """Check health of all API services"""
    status = APIServiceFactory.get_service_status()
    return {game: info['available'] for game, info in status.items()}
